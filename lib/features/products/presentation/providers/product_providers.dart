import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/firebase_service.dart';
import '../../data/datasources/firebase_products_datasource.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/category.dart';
import '../../domain/entities/product_filter.dart';
import '../../domain/entities/product_search_result.dart';

// Firebase service provider
final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return FirebaseService();
});

// Firebase products datasource provider
final firebaseProductsDatasourceProvider = Provider<FirebaseProductsDatasource>(
  (ref) {
    final firebaseService = ref.watch(firebaseServiceProvider);
    return FirebaseProductsDatasource(firebaseService);
  },
);

// Firebase products provider that fetches from Firebase
final firebaseProductsProvider = FutureProvider<List<Product>>((ref) async {
  final datasource = ref.watch(firebaseProductsDatasourceProvider);

  try {
    // Try to fetch from Firebase first
    final products = await datasource.getAllProducts();

    if (products.isNotEmpty) {
      return products;
    }

    // If no products in Firebase, return empty list as fallback
    return <Product>[];
  } catch (e) {
    // Fallback to empty list if Firebase fails
    return <Product>[];
  }
});

// Legacy provider for backward compatibility
final mockProductsProvider = Provider<List<Product>>((ref) {
  final productsAsync = ref.watch(firebaseProductsProvider);
  return productsAsync.when(
    data: (products) => products,
    loading: () => <Product>[], // Fallback during loading
    error: (_, __) => <Product>[], // Fallback on error
  );
});

// Simple product provider
final productProvider = FutureProvider.family<Product?, String>((
  ref,
  productId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(
    const Duration(milliseconds: 300),
  ); // Simulate network delay
  try {
    return products.firstWhere((p) => p.id == productId);
  } on StateError {
    return null;
  }
});

// Additional simple providers
final featuredProductsProvider = FutureProvider<List<Product>>((ref) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));
  return products.where((p) => p.isFeatured).toList();
});

final popularProductsProvider = FutureProvider<List<Product>>((ref) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));
  return products.where((p) => p.rating >= 4.5).toList();
});

final saleProductsProvider = FutureProvider<List<Product>>((ref) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));
  return products.where((p) => p.originalPrice != null).toList();
});

// Products provider with filter support
final productsProvider =
    FutureProvider.family<ProductSearchResult, ProductFilter>((
      ref,
      filter,
    ) async {
      final productsAsync = await ref.watch(firebaseProductsProvider.future);
      final products = productsAsync;
      await Future.delayed(const Duration(milliseconds: 500));

      // Apply filters to mock products
      var filteredProducts = products.where((product) {
        // Search query filter
        if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
          final query = filter.searchQuery!.toLowerCase();
          if (!product.name.toLowerCase().contains(query) &&
              !product.description.toLowerCase().contains(query) &&
              !product.tags.any((tag) => tag.toLowerCase().contains(query))) {
            return false;
          }
        }

        // Category filter
        if (filter.categoryIds.isNotEmpty) {
          if (!filter.categoryIds.contains(product.categoryId)) {
            return false;
          }
        }

        // Brand filter
        if (filter.brandIds.isNotEmpty) {
          if (!filter.brandIds.contains(product.brandId)) {
            return false;
          }
        }

        // Price range filter (adjusted for USD pricing)
        if (filter.priceRange.hasRange) {
          if (filter.priceRange.minPrice != null &&
              product.price < filter.priceRange.minPrice!) {
            return false;
          }
          if (filter.priceRange.maxPrice != null &&
              product.price > filter.priceRange.maxPrice!) {
            return false;
          }
        }

        // Rating filter
        if (filter.minRating != null && product.rating < filter.minRating!) {
          return false;
        }

        // Availability filter
        if (filter.availability != ProductAvailability.all) {
          switch (filter.availability) {
            case ProductAvailability.inStock:
              if (product.stockStatus != StockStatus.inStock) return false;
              break;
            case ProductAvailability.outOfStock:
              if (product.stockStatus != StockStatus.outOfStock) return false;
              break;
            case ProductAvailability.lowStock:
              if (product.stockStatus != StockStatus.lowStock) return false;
              break;
            case ProductAvailability.all:
              break;
          }
        }

        // Featured filter
        if (filter.isFeatured != null &&
            product.isFeatured != filter.isFeatured!) {
          return false;
        }

        // On sale filter
        if (filter.isOnSale != null) {
          final isOnSale = product.originalPrice != null;
          if (isOnSale != filter.isOnSale!) {
            return false;
          }
        }

        return true;
      }).toList();

      // Apply sorting
      switch (filter.sortBy) {
        case SortOption.priceAsc:
          filteredProducts.sort((a, b) => a.price.compareTo(b.price));
          break;
        case SortOption.priceDesc:
          filteredProducts.sort((a, b) => b.price.compareTo(a.price));
          break;
        case SortOption.ratingDesc:
          filteredProducts.sort((a, b) => b.rating.compareTo(a.rating));
          break;
        case SortOption.newest:
          filteredProducts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case SortOption.popular:
          filteredProducts.sort(
            (a, b) => b.reviewCount.compareTo(a.reviewCount),
          );
          break;
        case SortOption.nameAsc:
          filteredProducts.sort((a, b) => a.name.compareTo(b.name));
          break;
        case SortOption.nameDesc:
          filteredProducts.sort((a, b) => b.name.compareTo(a.name));
          break;
        case SortOption.relevance:
          // Keep original order for relevance
          break;
      }

      // Apply pagination
      final totalCount = filteredProducts.length;
      final totalPages = (totalCount / filter.limit).ceil();
      final startIndex = (filter.page - 1) * filter.limit;
      final endIndex = (startIndex + filter.limit).clamp(0, totalCount);

      final paginatedProducts = filteredProducts.sublist(
        startIndex.clamp(0, totalCount),
        endIndex,
      );

      return ProductSearchResult(
        products: paginatedProducts,
        totalCount: totalCount,
        currentPage: filter.page,
        totalPages: totalPages,
        hasNextPage: filter.page < totalPages,
        hasPreviousPage: filter.page > 1,
      );
    });

// Search products provider
final searchProductsProvider =
    FutureProvider.family<
      ProductSearchResult,
      ({String query, ProductFilter? filter})
    >((ref, params) async {
      final searchFilter = (params.filter ?? const ProductFilter()).copyWith(
        searchQuery: params.query,
      );
      return ref.watch(productsProvider(searchFilter).future);
    });

// Search suggestions provider
final searchSuggestionsProvider = FutureProvider.family<List<String>, String>((
  ref,
  query,
) async {
  if (query.length < 2) return [];

  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 200));

  final suggestions = <String>{};

  for (final product in products) {
    // Add product names that match
    if (product.name.toLowerCase().contains(query.toLowerCase())) {
      suggestions.add(product.name);
    }

    // Add brand names that match
    if (product.brandName.toLowerCase().contains(query.toLowerCase())) {
      suggestions.add(product.brandName);
    }

    // Add category names that match
    if (product.categoryName.toLowerCase().contains(query.toLowerCase())) {
      suggestions.add(product.categoryName);
    }

    // Add tags that match
    for (final tag in product.tags) {
      if (tag.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(tag);
      }
    }
  }

  return suggestions.take(5).toList();
});

// Category providers
final categoryByIdProvider = FutureProvider.family<Category?, String>((
  ref,
  categoryId,
) async {
  await Future.delayed(const Duration(milliseconds: 300));

  // Mock category data - in a real app this would fetch from repository
  return Category(
    id: categoryId,
    name: 'Sample Category',
    description: 'Sample category description',
    imageUrl: 'https://via.placeholder.com/300x200',
    iconUrl: 'https://via.placeholder.com/50x50',
    productCount: 25,
    subcategories: [
      Category(
        id: '${categoryId}_sub1',
        name: 'Subcategory 1',
        description: 'First subcategory',
        parentId: categoryId,
      ),
      Category(
        id: '${categoryId}_sub2',
        name: 'Subcategory 2',
        description: 'Second subcategory',
        parentId: categoryId,
      ),
    ],
    isActive: true,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    updatedAt: DateTime.now(),
  );
});

// Products by category provider
final categoryProductsProvider = FutureProvider.family<List<Product>, String>((
  ref,
  categoryId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));

  // Filter products by category - in a real app this would be done by the repository
  return products.where((p) => p.categoryId == categoryId).toList();
});

// Related products provider
final relatedProductsProvider = FutureProvider.family<List<Product>, String>((
  ref,
  productId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));

  // Get the current product to find related products
  final currentProduct = products.firstWhere(
    (p) => p.id == productId,
    orElse: () => products.first,
  );

  // Find products in the same category, excluding the current product
  final relatedProducts = products
      .where(
        (p) => p.id != productId && p.categoryId == currentProduct.categoryId,
      )
      .take(4)
      .toList();

  return relatedProducts;
});

// Frequently bought together provider
final frequentlyBoughtTogetherProvider =
    FutureProvider.family<List<Product>, String>((ref, productId) async {
      final products = ref.watch(mockProductsProvider);
      await Future.delayed(const Duration(milliseconds: 300));

      // Get the current product to find frequently bought together products
      final currentProduct = products.firstWhere(
        (p) => p.id == productId,
        orElse: () => products.first,
      );

      // Find popular products in the same category, excluding the current product
      final frequentlyBought = products
          .where(
            (p) =>
                p.id != productId &&
                p.categoryId == currentProduct.categoryId &&
                p.rating >= 4.0,
          )
          .take(3)
          .toList();

      return frequentlyBought;
    });

// Recently viewed products provider
final recentlyViewedProductsProvider =
    FutureProvider.family<List<Product>, String>((ref, userId) async {
      final products = ref.watch(mockProductsProvider);
      await Future.delayed(const Duration(milliseconds: 300));

      // Mock implementation - return a few random products as recently viewed
      // In a real app, this would fetch from user's viewing history
      return products.take(5).toList();
    });

// Extension methods for actions
extension ProductProviderActions on WidgetRef {
  void trackProductView(String productId) {
    // Mock implementation
  }

  void trackProductSearch(String query) {
    // Mock implementation
  }
}
