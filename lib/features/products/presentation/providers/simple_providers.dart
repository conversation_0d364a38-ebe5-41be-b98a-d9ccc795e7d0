import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';

import '../../domain/entities/product.dart';
import '../../domain/entities/brand.dart';
import 'product_providers.dart';

// Brand provider - fetches a single brand by ID
final brandProvider = FutureProvider.family<Brand?, String>((
  ref,
  brandId,
) async {
  await Future.delayed(
    const Duration(milliseconds: 300),
  ); // Simulate network delay

  // Mock brand data - in a real app this would fetch from repository
  return Brand(
    id: brandId,
    name: 'Sample Brand',
    description:
        'This is a sample brand description with detailed information about the brand.',
    logoUrl: 'https://via.placeholder.com/150x150',
    websiteUrl: 'https://example.com',
    countryOfOrigin: 'United States',
    isActive: true,
    isFeatured: true,
    productCount: 150,
    averageRating: 4.5,
    createdAt: DateTime.now().subtract(const Duration(days: 365)),
    updatedAt: DateTime.now(),
    email: '<EMAIL>',
    phone: '+**********',
    primaryColor: Colors.blue,
    isVerified: true,
    reviewCount: 1250,
    rating: 4.5,
  );
});

// Brand products provider - fetches all products for a specific brand
final brandProductsProvider = FutureProvider.family<List<Product>, String>((
  ref,
  brandId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));

  // Filter products by brand - in a real app this would be done by the repository
  return products.where((p) => p.brandId == brandId).toList();
});

// Brand popular products provider - fetches popular products for a specific brand
final brandPopularProductsProvider = FutureProvider.family<List<Product>, String>((
  ref,
  brandId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));

  // Filter products by brand and rating - in a real app this would be done by the repository
  return products.where((p) => p.brandId == brandId && p.rating >= 4.0).toList()
    ..sort((a, b) => b.rating.compareTo(a.rating));
});

// Brand new arrivals provider - fetches new products for a specific brand
final brandNewArrivalsProvider = FutureProvider.family<List<Product>, String>((
  ref,
  brandId,
) async {
  final products = ref.watch(mockProductsProvider);
  await Future.delayed(const Duration(milliseconds: 300));

  // Filter products by brand and sort by creation date - in a real app this would be done by the repository
  final brandProducts = products.where((p) => p.brandId == brandId).toList();
  brandProducts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

  // Return the newest products (last 30 days or top 10)
  final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
  final newProducts = brandProducts
      .where((p) => p.createdAt.isAfter(thirtyDaysAgo))
      .toList();

  // If no products in last 30 days, return the 10 newest
  if (newProducts.isEmpty) {
    return brandProducts.take(10).toList();
  }

  return newProducts;
});

// All brands provider - fetches all available brands
final brandsProvider = FutureProvider<List<Brand>>((ref) async {
  await Future.delayed(const Duration(milliseconds: 300));

  // Mock brands data - in a real app this would fetch from repository
  return [
    Brand(
      id: 'brand1',
      name: 'TechCorp',
      description: 'Leading technology brand',
      logoUrl: 'https://via.placeholder.com/150x150',
      countryOfOrigin: 'United States',
      isActive: true,
      isFeatured: true,
      productCount: 150,
      averageRating: 4.5,
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now(),
      isVerified: true,
      reviewCount: 1250,
      rating: 4.5,
    ),
    Brand(
      id: 'brand2',
      name: 'StyleHub',
      description: 'Fashion and lifestyle brand',
      logoUrl: 'https://via.placeholder.com/150x150',
      countryOfOrigin: 'France',
      isActive: true,
      isFeatured: true,
      productCount: 75,
      averageRating: 4.2,
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      updatedAt: DateTime.now(),
      isVerified: true,
      reviewCount: 890,
      rating: 4.2,
    ),
  ];
});

// Featured brands provider - fetches featured brands
final featuredBrandsProvider = FutureProvider<List<Brand>>((ref) async {
  final brands = await ref.watch(brandsProvider.future);
  await Future.delayed(const Duration(milliseconds: 200));

  // Return brands with high ratings as featured
  return brands.where((b) => (b.rating ?? b.averageRating) >= 4.0).toList();
});

// Extension methods for brand-related actions
extension BrandProviderActions on WidgetRef {
  void toggleFollowBrand(String userId, String brandId) {
    // Mock implementation - in a real app this would update the backend
    // In production, this would call a repository method
  }

  bool isBrandFollowed(String brandId) {
    // Mock implementation - in a real app this would check user's followed brands
    return false;
  }

  void trackBrandView(String brandId) {
    // Mock implementation - in a real app this would track analytics
    // In production, this would call an analytics service
  }
}
